import pandas as pd
import matplotlib.pyplot as plt
import japanize_matplotlib
import numpy as np
import os

# 結果ファイルのパス
mip_results_path = 'result/MIP_aggregate_results.csv'
multistart_results_path = 'result/multi_start_aggregate_results.csv'

print("=== 最適化手法比較サマリー ===")

# CSVファイルを読み込み
try:
    mip_df = pd.read_csv(mip_results_path, encoding='utf-8')
    print("MIP結果ファイルを読み込みました")
    print(mip_df)
except Exception as e:
    print(f"MIP結果ファイルの読み込みエラー: {e}")
    # エンコーディングを変更して再試行
    try:
        mip_df = pd.read_csv(mip_results_path, encoding='shift_jis')
        print("MIP結果ファイルを読み込みました（Shift_JIS）")
        print(mip_df)
    except Exception as e2:
        print(f"MIP結果ファイルの読み込みに失敗: {e2}")
        mip_df = None

try:
    multistart_df = pd.read_csv(multistart_results_path, encoding='utf-8')
    print("\n多スタートローカルサーチ結果ファイルを読み込みました")
    print(multistart_df)
except Exception as e:
    print(f"多スタートローカルサーチ結果ファイルの読み込みエラー: {e}")
    multistart_df = None

# データの前処理と比較
if mip_df is not None and multistart_df is not None:
    # 合計行を抽出
    mip_total = mip_df[mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]
    multistart_total = multistart_df[multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]
    
    if len(mip_total) > 0 and len(multistart_total) > 0:
        # 合計値を取得
        mip_objective = float(mip_total.iloc[0, 1])  # 目的関数値
        mip_time = float(mip_total.iloc[0, 2])       # 計算時間
        
        multistart_objective = float(multistart_total.iloc[0, 1])  # 目的関数値
        multistart_time = float(multistart_total.iloc[0, 2])       # 計算時間
        
        print(f"\n=== 合計値比較 ===")
        print(f"MIP - 目的関数値: {mip_objective:.2f}, 計算時間: {mip_time:.4f}秒")
        print(f"多スタートローカルサーチ - 目的関数値: {multistart_objective:.2f}, 計算時間: {multistart_time:.4f}秒")
        
        # 改善率の計算
        objective_improvement = ((multistart_objective - mip_objective) / mip_objective) * 100
        time_ratio = multistart_time / mip_time
        
        print(f"\n=== 比較結果 ===")
        print(f"目的関数値の差: {multistart_objective - mip_objective:.2f}")
        print(f"目的関数値の変化率: {objective_improvement:.2f}% (正の値は多スタートが悪い)")
        print(f"計算時間の比率: {time_ratio:.2f} (多スタート/MIP)")
    else:
        print("合計行が見つかりませんでした")
        mip_objective = mip_time = multistart_objective = multistart_time = None
else:
    print("データの読み込みに失敗したため、比較できません")
    mip_objective = mip_time = multistart_objective = multistart_time = None

# 個別データセットの比較も行う
if mip_df is not None and multistart_df is not None:
    # 合計行以外のデータを取得
    mip_individual = mip_df[~mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]
    multistart_individual = multistart_df[~multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]
    
    print(f"\n=== 個別データセット比較 ===")
    print(f"MIP個別結果:")
    print(mip_individual)
    print(f"\n多スタートローカルサーチ個別結果:")
    print(multistart_individual)

# 比較プロットの作成
if mip_objective is not None and multistart_objective is not None:
    # データの準備
    methods = ['MIP', '多スタートローカルサーチ']
    objectives = [mip_objective, multistart_objective]
    times = [mip_time, multistart_time]
    
    # プロットの作成
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 目的関数値の比較
    bars1 = ax1.bar(methods, objectives, color=['skyblue', 'lightcoral'], alpha=0.7)
    ax1.set_title('目的関数値の比較', fontsize=14, fontweight='bold')
    ax1.set_ylabel('目的関数値')
    ax1.grid(True, alpha=0.3)
    
    # 値をバーの上に表示
    for bar, value in zip(bars1, objectives):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.0f}', ha='center', va='bottom', fontweight='bold')
    
    # 計算時間の比較
    bars2 = ax2.bar(methods, times, color=['lightgreen', 'orange'], alpha=0.7)
    ax2.set_title('計算時間の比較', fontsize=14, fontweight='bold')
    ax2.set_ylabel('計算時間 (秒)')
    ax2.grid(True, alpha=0.3)
    
    # 値をバーの上に表示
    for bar, value in zip(bars2, times):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.4f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    
    # プロットを保存
    output_path = 'result/optimization_comparison.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"\n比較プロットを保存しました: {output_path}")
    
    plt.show()
    
    # 詳細比較テーブルの作成
    comparison_data = {
        '手法': ['MIP', '多スタートローカルサーチ'],
        '目的関数値': [f'{mip_objective:.2f}', f'{multistart_objective:.2f}'],
        '計算時間(秒)': [f'{mip_time:.4f}', f'{multistart_time:.4f}'],
        '相対性能': ['基準', f'{objective_improvement:+.2f}%'],
        '時間効率': ['基準', f'{time_ratio:.2f}倍']
    }
    
    comparison_df = pd.DataFrame(comparison_data)
    print(f"\n=== 詳細比較テーブル ===")
    print(comparison_df.to_string(index=False))
    
    # 比較テーブルをCSVとして保存
    comparison_csv_path = 'result/method_comparison_summary.csv'
    comparison_df.to_csv(comparison_csv_path, index=False, encoding='utf-8')
    print(f"\n比較テーブルを保存しました: {comparison_csv_path}")
    
else:
    print("データが不足しているため、プロットを作成できません")